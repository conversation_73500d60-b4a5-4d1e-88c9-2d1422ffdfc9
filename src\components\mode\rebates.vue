<template>
    <main>
      <header>
        <van-nav-bar :title="$t('main.rebates.label1')" left-arrow @click-left="$router.back()" />  <!-- 返水记录 -->
      </header>
  
  
  
      <div class="list">
        <van-list v-model="loading" :finished="finished" offset="50" @load="onLoad" class="tbody" :loading-text="`${ $t('foot.loading') }...`" :finished-text="`${ $t('main.lottery.label16') }`">
          <div class="tr" v-for="(item, index) in dealRecordList" :key="index" >
            <div class="row">
              <div class="label">{{ $t('main.rebates.label2') }}</div>
              <div class="value">{{ item.order_sn }}</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('main.rebates.label3') }}</div>
              <div class="value">{{ item.created_at }}</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('main.rebates.label4') }}</div>
              <div class="value">{{ item.balance }}KRW</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('main.rebates.label5') }}</div>
              <div class="value">{{ item.rate }}%</div>
            </div>
            <div class="row">
              <div class="label">{{ $t('main.rebates.label6') }}</div>
              <div class="value">{{ item.rate_money }}KRW</div>
            </div>
            <!-- <div class="row">
              <div class="label">{{ $t('main.rebates.label7') }}</div>
              <div class="value">{{ item.amount }}</div>
            </div> -->
          </div>
        </van-list>
      </div>
  
  
    </main>
  </template>
  <script>
  export default {
    name: 'message',
    data() {
      return {
        params: {
          page: 0
        },
        startPlaceholder: '',
        endPlaceholder: '',
        currentDate: new Date(),
        minDate: new Date(2020,0,1),
        maxDate: new Date(2030,0,1),
        startTimeShow: false,
        endTimeShow: false,
        loading: false,
        finished: false,
        dealRecordList: [],
        dealRecordData: {},
        statusInfo: []
      }
    },
    created() {
      // this.getDealRecordList()
      this.statusInfo = [
        this.$t('main.rebates.label5'),
        this.$t('main.rebates.label6'),
        this.$t('main.rebates.label7'),
      ]
    },
    methods: {
  
      async getDealRecordList(refresh = false) {
        const res = await this.$apiFun.getRateListApi(this.params)
        res.data.data.map(item => {
          console.log(item.opencode, item)
          if(item.opencode) {
            const l = item.opencode.split(',')
            item.total = l.reduce((s, t) => (Number(s) + Number(t)), 0)
            if(item.total == item.bet_code) {
              item.openStatus = 0
            }else {
              item.openStatus = 1
            }
          }else {
            item.openStatus = 2
          }
          return item
        })
  
        // 判断是刷新还是加载
        if (refresh) {
          this.dealRecordList = res.data.data
        } else {
          this.dealRecordList = [...this.dealRecordList, ...res.data.data]
        }
        this.dealRecordData = res.data
        this.loading = false
        if (this.params.page >= this.dealRecordData.last_page) {
          this.finished = true
        }
        this.$parent.loading = false
      },
  
  
      onLoad () {
        this.loading = true
        this.params.page++
        this.getDealRecordList(false)
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  header {
    height: 46px;
    .van-nav-bar {
      position: fixed;
      width: 100%;
      top: 0;
    }
    .right {
      position: fixed;
      display: flex;
      align-items: center;
      z-index: 1000;
      height: 46px;
      line-height: 46px;
      right: 20px;
      font-weight: 500;
      font-size: .35rem;
    }
  }
  
  
  .list {
    background: #F3F3F9;
    box-sizing: border-box;
    height: calc(var(--vh) * 100 - 47px);
    padding-top: 10px;
    overflow: auto;
    .tr {
      margin: 0px 15px;
      padding: 15px 10px ;
      background: #fff;
      border-radius: 5px;

      .row {
        display: flex;
        color: #98A8C5;
        justify-content: space-between;
        align-items: center;
        height: 30px;
        .label {
          color: #98A8C5;
        }
      }
    }
    .tr+.tr{
      margin-top: 10px;
    }
  }
  
  
  </style>