<template>
  <main>
    <!-- <div class="pageTop"> </div> -->
    <van-nav-bar class="pageTop" title="" left-arrow @click-left="$router.back()" />
    <div class="banner">
      <img src="/static/image/73b07f2.jpg" alt="">
    </div>
    <div class="lotteryList">
      <div class="nav">
        <van-tabs v-model="navActive" @change="changePage" class="gameBox">
          <van-tab :title="$t('mode.index.label13')">
            <div class="card">
              <div class="block" v-for="(item, index) in conciseList" :key="index" @click="goPage(item)">
                <img :src="item.mobile_img" alt="">
                <div class="label">{{ item.name_en }}</div>
              </div>
            </div>
          </van-tab>
          <van-tab :title="$t('mode.index.label26')">
            <div class="card">
              <div class="block" v-for="(item, index) in fishingList" :key="index" @click="goPage(item)">
                <img :src="item.mobile_img" alt="">
                <div class="label">{{ item.name_en }}</div>
              </div>
            </div>
          </van-tab>
          <van-tab :title="$t('mode.index.label12')">
            <div class="card">
              <div class="block" v-for="(item, index) in jokerList" :key="index" @click="goPage(item)">
                <img :src="item.mobile_img" alt="">
                <div class="label">{{ item.name_en }}</div>
              </div>
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </div>
  </main>
</template>
<script>
export default {
  name: 'caipiao',
  data() {
    return {
      navActive: 0,
      totalList: [],
      conciseList: [],
      fishingList: [],
      jokerList: [],
      title: '',
      params: {
        plat_name: '',
        game_category: ''
      },
      game_categoryList: [
        'concise',
        'fishing',
        'joker'
      ],
    };
  },
  created() {
    this.title = this.$route.query.name
    this.params.plat_name = this.$route.query.plat_name
    this.params.game_category = this.$route.query.game_category
    this.navActive = this.game_categoryList.indexOf(this.$route.query.game_category)
    this.getGameList()
  },
  methods: {
    async getGameList() {
      if(this.params.game_category === 'concise' && this.conciseList.length <= 0) {
        this.$parent.showLoading()
        const res = await this.$apiFun.getSonGameApi(this.params)
        if(res.code === 200) {
          this.conciseList = res.data
        }
      }else if(this.params.game_category === 'fishing' && this.fishingList.length <= 0) {
        this.$parent.showLoading()
        const res = await this.$apiFun.getSonGameApi(this.params)
        if(res.code === 200) {
          this.fishingList = res.data
        }
      }
      else if(this.params.game_category === 'joker' && this.jokerList.length <= 0) {
        this.$parent.showLoading()
        const res = await this.$apiFun.getSonGameApi(this.params)
        if(res.code === 200) {
          this.jokerList = res.data
        }
      }
      this.$parent.hideLoading();
    },

    changePage(e) {
      console.log(e)
      if(e === 0) {
        this.params.game_category = 'concise'
      }
      else if(e === 1) {
        this.params.game_category = 'fishing'
      }
      else if(e === 2) {
        this.params.game_category = 'joker'
      }
      this.getGameList()
    },


    goPage(item) {
      this.$parent.openGamePage(item.platform_name, item.game_code, item.category_id, item.lang)
    }
  },
  mounted() {
  },
};
</script>
<style lang="scss" scoped>
.pageTop {
  background: #fff;
  height: 46px;
  position: fixed;
}
.banner {
  margin-top: 46px;
  img {
    width: 100%;
    border-radius: 20px;
    object-fit: contain;
    display: block;
  }
}
.lotteryList {
  padding-top: 10px;
  background: rgb(237, 241, 255);
  box-sizing: border-box;
}

.nav {
  width: 90%;
  margin: 0 auto;
}
.card {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  gap: 10px;
  height: calc(var(--vh) * 100 - 46px - (100vw * .46) - 64px);
  overflow-y: auto;
  .block {
    width: calc((100%  - 30px) / 4);
    border-radius: 10px;
    text-align: center;
    box-sizing: border-box;
    img {
      width: 100%;
      display: block;
      margin: 0 auto;
      object-fit: contain;
      display: block;
      border-radius: 5px;
    }
    .label {
      font-size: 8px;
      text-align: center;
      font-weight: 500;
      font-weight: bold;
      margin: 5px 0;
    }
  }
}
</style>
