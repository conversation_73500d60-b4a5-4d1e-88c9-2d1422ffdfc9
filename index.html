<!DOCTYPE html>
<html  lang="zh-CN">

<head>
	<meta charset="utf-8">
  <meta name="format-detection" content="telephone=no"/>
	<meta http-equiv="X-UA-Compatible" content="edge">
    <meta name="google" content="notranslate" />
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
	<title></title>
	<!-- 适用于所有设备 -->
	<link rel="apple-touch-icon" href="/static/image/logo.png">
	
	<!-- <link rel="shortcut icon" type="image/x-icon" id="iconconfig"
		href="/static/image/uacPoGJlcBWACL_fAAAlvjvOsR4020.ico"> -->




	<style>
		*{
			font-size: 12px;
		}
		#redPacket {
			position: fixed;
			right: 12px;
			bottom: 60px;
			width: 75px;
			height: 85px;
			z-index: 999;
			cursor: pointer;
			background: url(/static/image/red-packet.png);
			background-size: 100% 100%;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		#redPacket .grab {

			margin-top: 20px;
			width: 35px;
			height: 20px;
			padding-top: 20px;
			background: url(/static/image/grab.png)no-repeat center;
			background-size: 100% 100%;
			-webkit-animation: scaleDraw .4s linear infinite;
		}

		#redPacket img {
			position: absolute;
			top: 0;
			right: -10px;
			width: 20px;
			-webkit-filter: grayscale(100%);
			-moz-filter: grayscale(100%);
			-ms-filter: grayscale(100%);
			-o-filter: grayscale(100%);
			filter: grayscale(100%);
			filter: gray;

		}

		@keyframes scaleDraw {
			25% {
				transform: scale(1.1);
			}

			50% {
				transform: scale(1.2);
			}

			75% {
				transform: scale(1.1);
			}

			100% {
				transform: scale(1);
			}
		}
	</style>
	<link rel="stylesheet" type="text/css" href="/static/css/swiper.css">
	<script src="/static/js/jquery-1.8.2.js"></script>
	<script src="/static/js/swiper.min.js"></script>
</head>

<body >

	<div id="app"></div>
</body>

</html>

<script>


</script>
