<template>
  <main>
    <header>
      <van-nav-bar :title="$t('main.trans.label1')" left-arrow @click-left="$router.back()" />
    </header>



    <div class="list">
      <div class="input_wrap">
        <van-field ref="input" v-model="params.username" type="text" :placeholder="$t('main.trans.label2')" autocomplete="off" />
      </div>
      <div class="input_wrap">
        <van-field ref="input" v-model="params.amount" type="number" :placeholder="$t('main.trans.label3')" autocomplete="off" />
      </div>
      <div class="input_wrap">
        <van-field ref="input" v-model="params.password" type="password" :placeholder="$t('main.trans.label4')" autocomplete="off" />
      </div>

      <div class="button" @click="submit">{{ $t('main.dealRecord.label6') }}</div>
    </div>

  </main>
</template>
<script>
export default {
  name: 'message',
  data() {
    return {
      params: {
        password: '',
        username: '',
        amount: '',
      },
    }
  },
  created() {
  },
  methods: {
    async submit() {
      this.showLoading()
      const res = await this.$apiFun.transApi(this.params)
      if(res.code == 200) {
        this.$parent.showTost('1', res.message)
      }
      this.hideLoading()
    },
    showLoading() {
      this.$parent.showLoading();
    },
    hideLoading() {
      this.$parent.hideLoading();
    },
  },
};
</script>
<style lang="scss" scoped>
header {
  height: 60px;
  .van-nav-bar {
    position: fixed;
    width: 100%;
    top: 0;
  }
  .right {
    position: fixed;
    display: flex;
    align-items: center;
    z-index: 1000;
    height: 46px;
    line-height: 46px;
    right: 20px;
    font-weight: 500;
    font-size: .35rem;
  }
}

.list {
  margin: 0 10px;
  .input_wrap {
    border: 1px solid #999;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 20px;
  }
  .button {
    height: 36px;
    line-height: 36px;
    text-align: center;
    background: #EA4236;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
  }
}
</style>
