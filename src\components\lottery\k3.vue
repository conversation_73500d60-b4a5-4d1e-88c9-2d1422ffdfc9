<template>
  <main>
    <div class="nav">
      <div class="left">
        <span>{{ $t('main.lottery.label1') }} {{ userInfo.balance || '0.00' }} KRW</span>
      </div>
      <div class="right">
        <van-dropdown-menu active-color="#CF866B">
          <van-dropdown-item v-model="typeName" :options="k3List" @change="switchType">
          </van-dropdown-item>
        </van-dropdown-menu>
      </div>
    </div>

    <div class="card">
      <div class="left">
        <div class="label">{{ openTimeInfo.last_expect }}{{ $t('main.lottery.label2') }}</div>
        <div class="imgList" @click="openDialog">
          <img :src="lastOpenInfo[0] === 0 ? '/static/image/caipiao/touzi.gif': `/static/image/caipiao/${lastOpenInfo[0]}.png`" />
          <img :src="lastOpenInfo[1] === 0 ? '/static/image/caipiao/touzi.gif': `/static/image/caipiao/${lastOpenInfo[1]}.png`" />
          <img :src="lastOpenInfo[2] === 0 ? '/static/image/caipiao/touzi.gif': `/static/image/caipiao/${lastOpenInfo[2]}.png`" />
          <img src="/static/image/caipiao/arrow-down.png" class="arrow"/>
        </div>
      </div>
      <div class="right">
        <div class="label">{{ $t('main.lottery.label13') }}{{ openTimeInfo.current_expect }}{{ $t('main.lottery.label3') }}</div>
        <van-count-down v-show="openTimeInfo.reamin_time" :time="openTimeInfo.reamin_time * 1000" @finish="getCaipiaoOpenTime" @change="changeTime"/>
      </div>
    </div>

    <div class="lotteryList">
      <div class="blockList">
        <div class="block" v-for="(item, index) in playInfo" :key="index" @click="chooseBlock(item, index)" :class="{ active: item.choose }">
          <div class="title">{{ item.class3 }}</div>
          <div class="label">
            <span>{{ $t('main.lottery.label14') }}</span>
            <span>{{ item.rate }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="bottom">
      <div class="buttonList">
        <div class="cancel" @click="cancel">{{ $t('main.lottery.label4') }}</div>
        <div class="submit" @click="submit">{{ $t('main.lottery.label5') }}</div>
      </div>
      <div class="orderInfo" v-show="chooseList.length > 0">
        <div class="choose">
          <span>{{ $t('main.lottery.label6') }}</span>
          <span v-for="(item, index) in chooseList" :key="index" class="num">{{ item.class3 }}</span>
        </div>
        <div class="filed">
          <span>{{ $t('main.lottery.label7') }}</span>
          <input type="text" v-model="price">
          <span>{{ $t('main.lottery.label8') }}</span>
          <span>{{ totalPrice }}</span>
          <span>KRW</span>
        </div>
        <div>
          <span>{{ $t('main.lottery.label9') }}{{ chooseList.length }}{{ $t('main.lottery.label15') }}，</span>
          <span>{{ chooseList.length * price }}KRW</span>
        </div>
        <div class="priceList">
          <div class="price" v-for="(item, index) in priceList" :key="index" @click="price = item">{{ item }}</div>
        </div>
      </div>
    </div>

    <van-dialog v-model="dialogShow" :closeOnClickOverlay="true" :showConfirmButton="false">
      <div class="table">
        <div class="thead">
          <div class="th">{{ $t('main.lottery.label10') }}</div>
          <div class="th">{{ $t('main.lottery.label11') }}</div>
          <div class="th">{{ $t('main.lottery.label12') }}</div>
        </div>
        <van-list v-model="loading" :finished="finished" offset="50" @load="onLoad" class="tbody" :key="listKey" :loading-text="`${ $t('foot.loading') }...`" :finished-text="`${ $t('main.lottery.label16') }`">
          <div class="tr" v-for="(item, index) in lastOpenCodeList" :key="index" >
            <div class="td">{{ item.expect }}</div>
            <div class="td">{{ item.opencode }}</div>
            <div class="td">{{ item.total }}</div>
          </div>
        </van-list>
      </div>
    </van-dialog>
  </main>
</template>
<script>
export default {
  name: 'caipiao',
  data() {
    return {
      k3List: [],
      typeName: '',
      pageName: '',
      userInfo: {},
      playInfo: [],
      openTimeInfo: {},
      countDown: 30 * 60 * 60 * 1000,
      lastOpenInfo: [0,0,0],
      chooseList: [],
      price: '',
      priceList: [5, 10, 50, 100, 1000],
      dialogShow: false,
      lastOpenCodeList: [],
      lastOpenCodeData: [],
      lastOpenCodeParams: {
        lottery_name: '',
        page: 1,
      },
      loading: false,
      finished: false,
      listKey: 0
    };
  },
  created() {
    this.$parent.showLoading();
    const query = this.$route.query;
    this.typeName = query.name
    this.userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')

    this.getCaipiaoList() //彩票列表

    this.getCaipiaoPlay() //彩票玩法

    this.getCaipiaoOpenTime() //开奖时间

    // this.getCaipiaoOpenCode() //獲取彩票歷史開獎記錄
  },
  computed: {
    totalPrice() {
      let maxRate = 0
      for(let i in this.chooseList) {
        if(Number(this.chooseList[i].rate) > maxRate) {
          maxRate = Number(this.chooseList[i].rate)
        }
      }
      return (maxRate * this.price).toFixed(3)
    }
  },
  methods: {
    // 彩票列表
    async getCaipiaoList() {
      const res = await this.$apiFun.getCaipiaoListApi()
      const k3List = []
      const pk10List = []
      for(let i in res.data) {
        if(res.data[i].typeid === 'k3') {
          res.data[i].text = res.data[i].title
          res.data[i].value = res.data[i].name
          k3List.push(res.data[i])
        }
        if(res.data[i].name === this.typeName) {
          this.pageName = res.data[i].title
        }
      }
      this.k3List = k3List
    },

    // 彩票玩法
    async getCaipiaoPlay() {
      const params = { typeid: 'k3' }
      const res = await this.$apiFun.getCaipiaoPlayApi(params)
      if(res.code === 200) {
        this.playInfo = res.data.map(item => {
          item.choose = false
          return item
        })
      }
    },

    // 开奖时间
    async getCaipiaoOpenTime() {
      const params = { lottery_name: this.typeName }
      const res = await this.$apiFun.getCaipiaoOpenTimeApi(params)
      this.$parent.hideLoading();
      if(res.code === 200) {
        this.openTimeInfo = res.data
        this.getCaipiaoLastOpen(res.data.last_expect)
      }

    },

    // 获取上一次开奖结果
    async getCaipiaoLastOpen(expect) {
      const params = { lottery_name: this.typeName, expect }
      const res = await this.$apiFun.getCaipiaoLastOpenApi(params)
      const list = res.data.opencode.split(',')
      this.lastOpenInfo[0] = list[0]
      this.$set(this.lastOpenInfo, 0, list[0])
      setTimeout(() => {
        this.$set(this.lastOpenInfo, 1, list[1])
        setTimeout(() => {
          this.$set(this.lastOpenInfo, 2, list[2])
        }, 1000)
      }, 1000)
    },

    // 獲取彩票歷史開獎記錄
    async getCaipiaoOpenCode(refresh = false) {
      this.lastOpenCodeParams.lottery_name = this.typeName
      const res = await this.$apiFun.getCaipiaoOpenCodeApi(this.lastOpenCodeParams)
      res.data.data.map(item => {
        const l = item.opencode.split(',')
        item.total = l.reduce((s, t) => (Number(s) + Number(t)), 0)
        return item
      })
      // 判断是刷新还是加载
      if (refresh) {
        this.lastOpenCodeList = res.data.data
      } else {
        this.lastOpenCodeList = [...this.lastOpenCodeList, ...res.data.data]
      }
      this.lastOpenCodeData = res.data
      this.loading = false
      if (this.lastOpenCodeParams.page >= this.lastOpenCodeData.last_page) {
        this.finished = true
      }
    },

    openDialog() {
      this.dialogShow = true
      this.loading = true
      this.finished = false
      this.lastOpenCodeParams.page = 0
      this.lastOpenCodeList = []
      this.listKey = Math.random() * 999
      this.getCaipiaoOpenCode(true)
    },


    onLoad () {
      this.loading = true
      this.lastOpenCodeParams.page++
      this.getCaipiaoOpenCode(false)
    },

    // 選擇投注
    chooseBlock(e, index) {
      e.choose = !e.choose
      this.$set(this.playInfo, index, e)
      if (e.choose) {
        this.chooseList.push(e)
      }else {
        let index = this.chooseList.findIndex(item => item.class3 === e.class3)
        this.chooseList = [...this.chooseList.slice(0, index), ...this.chooseList.slice(index+1, this.chooseList.length)]
      }
    },

    switchType(e) {
      this.typeName = e
      this.lastOpenInfo = [0,0,0]
      this.$parent.showLoading();
      this.getCaipiaoOpenTime()
    },

    cancel() {
      this.chooseList = []
      for(let i in this.playInfo) {
        this.playInfo[i].choose = false
      }
      this.$set(this, 'payInfo', this.playInfo)
      this.price = ''
    },

    async submit() {
      if(this.chooseList.length === 0) {
        this.$parent.showTost(0, this.$t('main.lottery.toast1'));
        return
      }
      if(this.price === 0 || !this.price) {
        this.$parent.showTost(0, this.$t('main.lottery.toast2'));
        return
      }
      const params = {
        lottery_name: this.typeName,
        expect: this.openTimeInfo.current_expect,
        order_list: []
      }
      for(let i in this.chooseList) {
        params.order_list.push({
          play_id: this.chooseList[i].id,
          amount: this.price
        })
      }
      const res = await this.$apiFun.lotterySubmitApi(params)
      if(res.code === 200) {
        this.$parent.showTost('1', res.message)
        this.cancel()
      }
    },

    changeTime(e) {
      if(e.minutes === 0 && e.seconds === 5) {
        this.$dialog.alert({
          message: `${this.openTimeInfo.current_expect}${this.$t('main.lottery.toast3')}`,
          confirmButtonText: this.$t('main.dealRecord.label6'),
          cancelButtonText: this.$t('main.dealRecord.label7'),
        })
        setTimeout(() => {
          this.$dialog.close()
        }, 7000)
      }
    }
  },
  mounted() {
  },
};
</script>
<style lang="scss" scoped>
main {
  background: #f1f1f1;
  position: relative;
}
.nav {
  height: 35px;
  display: flex;
  padding: 30px 15px 0;
  justify-content: space-between;
  background: #fff;
  .left span {
    color: #98A8C5;
    font-size: .35rem;
  }
  .right span {
    font-weight: 500;
    font-size: .4rem;
  }
}

.card {
  margin: 15px 0;
  padding: 15px 15px 0;
  height: 73px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  .label {
    color: #4E6693;
    font-size: .33rem;
  }
  img {
    width: 30px;
    margin: 10px;
    margin-left: 0;
  }
  .van-count-down {
    color: #4E6693;
    font-size: .7rem;
    line-height: 53px;
    text-align: right;
  }
  .imgList {
    display: flex;
    align-items: center;
  }
  .arrow {
    width: 15px;
  }
}

.lotteryList {
  background: #fff;
  // height: calc(var(--vh) * 100 - 65px - 118px - 190px);
  overflow-y: auto;
  padding-bottom: 190px;
  .blockList {
    display: flex;
    padding: 15px;
    gap: 10px;
    flex-wrap: wrap;
    box-sizing: border-box;
    .block {
      width: calc((100% - 30px) / 4);
      height: 70px;
      box-sizing: border-box;
      border: 1px solid #C9C9C9;
      border-radius: 10px;
      .title {
        color: #CF866B;
        text-align: center;
        font-size: .6rem;
        font-weight: 500;
        margin-top: 10px;
      }
      .label {
        color: #CF866B;
        text-align: center;
        font-size: .3rem;
        font-weight: 500;
      }
    }
    .active {
      background: #F1F1F1;
      border-color: #f1f1f1;
    }
  }
}

.bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  .buttonList {
    display: flex;
    gap: 15px;
    padding: 15px;
    .cancel {
      width: 70px;
      height: 36px;
      line-height: 36px;
      text-align: center;
      background: #419efc;
      border-radius: 3px;
      color: #fff;
      font-size: 14px;
    }
    .submit {
      flex: 1;
      height: 36px;
      line-height: 36px;
      text-align: center;
      background: #EA4236;
      border-radius: 3px;
      color: #fff;
      font-size: 14px;
    }
  }

  .orderInfo {
    position: absolute;
    background: #fff;
    width: 100%;
    top: -125px;
    border-top: 1px solid #CF866B;
    padding: 10px 15px;
    .num {
      color: #CF866B;
      padding: 0 2.5px;
      font-size: .35rem;
    }
    span {
      font-size: .35rem;
    }

    .filed {
      margin: 10px 0;
    }

    input {
      width: 70px;
      text-align: center;
      border-color: #c9c9c9;
      margin: 0 8px;
    }

    .priceList {
      display: flex;
      margin-top: 10px;
      .price {
        background: #CF866B99;
        border-radius: 5px;
        padding: 6px 15px;
        margin-right: 10px;
        color: #fff;
      }
    }
  }
}


.van-dialog {
  width: 90vw;
  height: calc(var(--vh) * 60);
  .thead {
    display: flex;
    padding: 10px 15px;
    height: 40px;
    box-sizing: border-box;
    align-items: center;
    .th {
      flex: 1;
      text-align: center;
    }
  }
  .tbody {
    height: calc(var(--vh) * 60 - 40px);
    overflow-y: auto;
  }
  .tr {
    display: flex;
    height: 25px;
    line-height: 25px;
    .td {
      flex: 1;
      text-align: center;
      color: #4E6693;
    }
  }
}


</style>

<style lang="scss">

.van-dropdown-menu__bar {
  height: 17px;
  box-shadow: none;
}

.van-dropdown-item {
  top: 65px !important;
  .van-cell__title span {
    font-size: .34rem !important;
  }
}

.van-dropdown-menu {
  height: 17px !important;

  .van-ellipsis {
    font-size: .4rem;
  }
}

</style>
