<template>
  <div class="content">
    <div class="head">{{ $t('kefu.tip1') }}</div>
    <div class="card" @click="goPage('url')">
      <van-image src="static/image/kefu/meiqia2.png"></van-image>
    </div>
    <div class="card" @click="goPage('telegram_url')">
      <van-image src="static/image/kefu/telegram.png"></van-image>
    </div>
    <div class="card" @click="goPage('line_url')">
      <van-image src="static/image/kefu/line.png"></van-image>
    </div>
    <div class="bottom">{{ $t('kefu.tip2') }}</div>
    <div class="bottom">{{ $t('kefu.tip3') }}</div>
  </div>
</template>


<script>

export default {
  name: 'kefu',
  data() {
    return {
      kefuList: [],
    };
  },
  mounted() {
    this.getkefu()
  },
  methods: {
    async getkefu () {
      const res = await this.$apiFun.post('/api/getservicerurl', {})
      if(res.code === 200) {
        this.kefuList = res.data
      }
    },

    goPage(e) {
      if(this.kefuList[e]) {
        window.open(this.kefuList[e])
      }
    },
  },
}
</script>


<style lang="scss" scoped>
.content {
  height: calc(var(--vh) * 100 - 68px);
  overflow: auto;
  .head {
    background: #d7f1fd;
    display: flex;
    padding: 16px;
    font-weight: 500;
    font-size: .34rem;
  }
  .card {
    margin: 20px 30px;
    border-radius: 10px;
    overflow: hidden;
    .van-image {
      display: block;
    }
  }
  .bottom {
    background: #d7f1fd;
    padding: 8px 16px;
    font-weight: 500;
    font-size: .34rem;
  }
}
</style>
