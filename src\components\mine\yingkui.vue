<template>
  <main>
    <header>
      <van-nav-bar :title="$t('yingkui.label1')" left-arrow @click-left="$router.back()" />
      <div class="right">
        <van-dropdown-menu active-color="#CF866B">
          <van-dropdown-item v-model="params.type" :options="typeList" @change="changeType">
          </van-dropdown-item>
        </van-dropdown-menu>
      </div>
    </header>

    <div class="wrap">
      <div class="line">
        <div class="card">
          <div class="title">{{ title }}{{ $t('yingkui.label7') }}</div>
          <div class="price" :class="{ red: value < 0, green: value > 0 }">
            <span>₩</span>
            <countTo :startVal='startValue' :endVal='value' :duration='2000'></countTo>
          </div>
          <div class="row">
            <div class="label">{{ $t('yingkui.label8') }}:</div>
            <div class="val">₩ {{ bet_money }}</div>
          </div>
          <div class="row">
            <div class="label">{{ $t('yingkui.label9') }}:</div>
            <div class="val">₩ {{ win_money }}</div>
          </div>
        </div>
      </div>
      <div class="yes">
        <div class="title">{{ $t('yingkui.label10') }}</div>
        <div class="price" :class="{ red: yesValue < 0, green: yesValue > 0 }">₩ {{ yesValue }}</div>
      </div>
    </div>


  </main>
</template>
<script>
import countTo from 'vue-count-to';
export default {
  components: { countTo },
  name: 'message',
  data() {
    return {
      params: {
        type: 0,
      },
      startValue: 0,
      value: 0,
      yesValue: 0,
      bet_money: 0,
      win_money: 0,
    }
  },
  created() {
    this.typeList = [
      { text: this.$t('yingkui.label2'), value: 0 },
      // { text: this.$t('yingkui.label3'), value: 1 },
      { text: this.$t('yingkui.label4'), value: 2 },
      { text: this.$t('yingkui.label5'), value: 6 },
      { text: this.$t('yingkui.label6'), value: 14 },
    ]
    this.$parent.loading = true
    this.getData()
    this.getYesterdayData()
  },
  computed: {
    title (){
      return this.typeList.filter(item => item.value === this.params.type)[0].text
    }
  },
  methods: {
    async getData() {
      const res = await this.$apiFun.getYingkuiApi(this.params)
      this.$parent.loading = false
      this.value = res.data.win_loss
      this.bet_money = res.data.bet_money
      this.win_money = res.data.win_money
    },

    changeType(e) {
      this.$parent.loading = true
      this.params.type = e
      this.getData()
    },
    async getYesterdayData() {
      const res = await this.$apiFun.getYingkuiApi({type: 1})
      this.yesValue = res.data.win_loss
    },
  },
  beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
main {
  min-height: calc(var(--vh) * 100);
  background: linear-gradient(180deg, #EFF2FE 5.46%, #E8EDFF 30.35%, #F6F8FF 97.83%);;
}
.van-nav-bar {
  background: transparent !important;
}
::v-deep .van-nav-bar__title {
  color: #5D83B2 !important;
}
header {
  height: 60px;
  .van-nav-bar {
    position: fixed;
    width: 100%;
    top: 0;
  }
  .right {
    position: fixed;
    display: flex;
    align-items: center;
    z-index: 1000;
    height: 46px;
    line-height: 46px;
    right: 20px;
    font-weight: 500;
    font-size: .35rem;
  }
}

.wrap {
  .line{
    position: fixed;
    width: 232vw;
    height: 232vw;
    border: 2px solid #fff;
    border-radius: 50%;
    top: -160vw;
    left: calc(100vw - 166.7vw);
    background: #F4F6FC;
    overflow: hidden;
  }

  .card {
    background: linear-gradient(180deg, #EFF2FE 0%, #E8EDFF 100%);
    border: 2px solid #fff;
    border-bottom: 0;
    padding: 5.3vw 6.9vw;
    border-radius: 20px 20px 0 0;
    margin: 6.9vw;
    height: 68.83vw;
    box-sizing: border-box;
    
    
    position: relative;
    top: 169.7vw;
    width: calc(100vw - 13.95vw);
    box-sizing: border-box;
    left: 66.2vw;
    .row {
      display: flex;
      padding: 10px 0;
      align-items: center;
      .label {
        color: #5D75AB;
        margin-right: 10px;
        font-size: 14px;
        line-height: 14px;
      }
      .val {
        color: #000;
        font-size: 14px;
        line-height: 14px;
      }
    }
  }

  .yes {
    position: relative;
    top: 65vw;
  }

  .title {
    text-align: center;
    color: #5D75AB;
    font-size: 16px;
  }
  .price {
    font-size: 26px;
    text-align: center;
    color: #888;
    padding: 10px 0;
    span {
      font-size: 26px;
      text-align: center;
      color: #888;
      padding: 10px 0;
    }
  }
    .red {
      color: #C41717;
      span {
        color: #C41717; 
      }
    }
    .green {
      color: #00AC2E;
      span {
        color: #00AC2E; 
      }
    }
}


</style>


<style lang="scss">

.van-dropdown-menu__bar {
  height: 17px !important;
  box-shadow: none !important;
  background: none !important;
}

.van-dropdown-item {
  top: 46px !important;
  .van-cell__title span {
    font-size: .34rem !important;
  }
}

.van-dropdown-menu {
  height: 17px !important;

  .van-ellipsis {
    font-size: .4rem;
    font-weight: 500;
  }
}

.van-dropdown-menu__title:after {
  border-color: transparent transparent #333 #333;
}

.van-dropdown-menu__title--active:after {
  border-color: transparent transparent currentColor currentColor;
}
</style>
