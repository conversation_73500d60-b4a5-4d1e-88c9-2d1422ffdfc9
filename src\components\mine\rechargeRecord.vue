<template>
  <main>
    <van-nav-bar class="pageTop" :title="$t('main.transRecord.label24')" left-arrow @click-left="$router.back()" />
    <div class="card">
      <div class="nav">
        <div class="block" @click="showPopup(1)">{{ name }}</div>
        <div class="block" @click="showPopup(2)">{{ dateName[params.date] }}</div>
      </div>
      <van-list v-model="loading" :finished="finished" offset="50" @load="onLoad" class="tbody" :loading-text="`${ $t('foot.loading') }...`" :finished-text="`${ $t('main.lottery.label16') }`">
        <div class="tr" v-for="(item, index) in rechargeList" :key="index" >
          <div class="row">
            <div>{{ $t('main.transRecord.label2') }}{{ item.out_trade_no }}</div>
            <div class="btn" v-if="item.state === 1" @click="cancel(item)">{{ $t('main.transRecord.label22') }}</div>
          </div>
          <div class="row">
            <div>{{ $t('main.transRecord.label3') }} :{{ item.amount }}</div>
            <div>{{ item.pay_way }}</div>
            <div style="margin-right: 5px">{{ stateType12[item.state] }}</div>
          </div>
          <div class="row">
            <div class="label">{{ item.created_at }}</div>
          </div>
        </div>
      </van-list>

      
      <div v-if="rechargeList.length === 0" style="margin-top: 60px; text-align: center">
        <img src="/static/image/mescroll-empty.png" style="width: 35%" alt="" />
        <van-divider dashed :style="{ color: '#ccc', borderColor: '#ccc', padding: '20px ' }">{{ $t('main.transRecord.label4') }}</van-divider>
      </div>
    </div>
    <!-- 弹出层 -->
    <van-popup v-model="popup" position="bottom" :style="{ height: 'calc(100% - 1.9rem - 46px)' }">
      <div class="lisg" v-if="showXuan == 1">
        <div class="bs" v-for="(item, index) in typeList" :key="index" @click="changDogame(item.name, item.platname)">
          <div :class="params.api_type == item.platname ? 'lisga act' : 'lisga'">{{ item.name }}</div>
        </div>
      </div>
      <div class="lisg" v-if="showXuan == 2">
        <div class="bs" @click="changtype('date', 1)">
          <div :class="params.date == 1 ? 'lisga act' : 'lisga'">{{ $t('main.transRecord.label5') }}</div>
        </div>
        <div class="bs" @click="changtype('date', 2)">
          <div :class="params.date == 2 ? 'lisga act' : 'lisga'">{{ $t('main.transRecord.label6') }}</div>
        </div>
        <div class="bs" @click="changtype('date', 3)">
          <div :class="params.date == 3 ? 'lisga act' : 'lisga'">{{ $t('main.transRecord.label7') }}</div>
        </div>
        <div class="bs" @click="changtype('date', 4)">
          <div :class="params.date == 4 ? 'lisga act' : 'lisga'">{{ $t('main.transRecord.label8') }}</div>
        </div>
      </div>
    </van-popup>
  </main>
</template>
<script>
export default {
  name: 'caipiao',
  data() {
    return {
      rechargeList: [],
      rechargeData: [],
      typeList: [],
      dateName: [],
      name: '', 
      params: {
        api_type: '',
        date: 4,
        page: 1,
        type: 1
      },
      loading: false,
      finished: false,
      showXuan: 1,
      popup: false
    };
  },
  created() {
    this.dateName = [
      '',
      this.$t('main.transRecord.label5'),
      this.$t('main.transRecord.label6'),
      this.$t('main.transRecord.label7'),
      this.$t('main.transRecord.label8'),
    ]
    this.stateType12 = [
      this.$t('main.transRecord.label14'),
      this.$t('main.transRecord.label15'),
      this.$t('main.transRecord.label16'),
      this.$t('main.transRecord.label17'),
      this.$t('main.transRecord.label23'),
    ]
    this.name = this.$t('main.transRecord.label13')
    this.getTypeList()
  },
  methods: {
    async getTypeList() {
      const res = await this.$apiFun.post('/api/balancelist')
      if (res.code == 200) {
        this.typeList = res.data;
        this.typeList.unshift({ name: this.$t('main.transRecord.label13'), platname: '' });
      }
    },
    async getRechargeList(refresh) {
      this.$parent.showLoading();
      const res = await this.$apiFun.post('/api/gettransrecord', this.params)
      // 判断是刷新还是加载
      if (refresh) {
        this.rechargeList = res.data.data
      } else {
        this.rechargeList = [...this.rechargeList, ...res.data.data]
      }
      this.rechargeData = res.data
      this.loading = false
      if (this.params.page >= this.rechargeData.last_page) {
        this.finished = true
      }
      this.$parent.hideLoading()
    },

    showPopup(val) {
      this.popup = true;
      this.showXuan = val;
    },
    
    onLoad () {
      this.loading = true
      this.getRechargeList(false)
      this.params.page++
    },

    async cancel(e) {
      this.$parent.showLoading();
      const res = await this.$apiFun.cancelRechargeApi({ out_trade_no: e.out_trade_no })
      if(res.code === 200) {
        this.params.page = 1
        this.finished = false
        this.loading = true
        this.getRechargeList(true)
      }else {
      this.$parent.hideLoading()
      }
    },
  },
};
</script>
<style lang="scss" scoped>
main {
  height: calc(var(--vh) * 100);
  background: rgb(237, 241, 255);
}
.pageTop {
  background: rgb(237, 233, 231);
  height: 46px;
  position: relative;
}

.card {
  height: calc(var(--vh) * 100 - 46px - 15px);
  margin: 0 10px;
  padding: 10px 15px;
  background: #fff;
  border-radius: 8px;
  
  .nav {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 1.1rem;
    box-sizing: border-box;
    padding: 0 12px;
    .block {
      height: 0.8rem;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 30%;
      background: #f7f8fc;
      border-radius: 1.1rem;
      font-size: 0.3rem;
    }
  }

  .tr {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
    border-bottom: 1px solid #E9EDF6;
    padding: 5px 0;
    .row {
      width: 100%;
      display: flex;
      height: 24px;
      align-items: center;
      justify-content: space-between;
    }
    .label {
      color: #98A8C5;
    }
    .btn {
      padding: 3px 10px;
      background: #98A8C5;
      border-radius: 3px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.lisg {
  box-sizing: border-box;
  padding: 10px 8px;
  display: flex;
  flex-wrap: wrap;
  .bs {
    width: 25%;
    height: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    .lisga {
      width: calc(100% - 8px);
      height: 0.9rem;
      border: 0.02rem solid #cbced8;
      border-radius: 0.08rem;
      color: #a5a9b3;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.2rem;
      text-align: center;
    }
    .lisga.act {
      background: #cf866b;
      color: #fff;
      border: none;
    }
  }
}
</style>
