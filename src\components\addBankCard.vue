<template>
  <div style="width: 100%; min-height: 100vh; background: rgb(237, 241, 255)">
    <van-nav-bar style="position: fixed; top: 0; left: 0; width: 100%; background-color: #ede9e7" :title="$t('main.addBankCard.title')" left-arrow @click-left="$router.back()" />
    <div style="height: 46px"></div>
    <!-- <div class="usrse">
      <div class="hgs">
        <div class="nams">{{ $t('main.addBankCard.label1') }}</div>
        <div>
          <div data-v-a12ec382="" class="van-cell-group van-hairline--top-bottom">
            <div data-v-a12ec382="" class="van-cell van-field">
              <div class="van-cell__value van-cell__value--alone van-field__value">
                <div class="van-field__body"><input type="text" auto-complete="off"  v-model="cardInfo.bank_owner" :placeholder="$t('main.addBankCard.placeholder1')" class="van-field__control" /></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style="font-size: 0.24rem; color: #98a5b3; text-align: center; box-sizing: border-box; padding: 6px">{{ $t('main.addBankCard.label2') }}</div> -->
    <div class="usrse">
      <div class="hgs" @click="changShow">
        <div class="nams">{{ $t('main.addBankCard.label3') }}</div>
        <div style="border-bottom: 1px solid #f2f2f2">
          <div data-v-a12ec382="" class="van-cell-group van-hairline--top-bottom">
            <div data-v-a12ec382="" class="van-cell van-field">
              <div class="van-cell__value van-cell__value--alone van-field__value">
                <div class="van-field__body">
                  <input type="text" auto-complete="off"  v-model="cardInfo.bank" readonly :placeholder="$t('main.addBankCard.placeholder2')" class="van-field__control" />
                  <van-icon name="arrow" color="#999"> </van-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="hgs">
        <div class="nams">{{ $t('main.addBankCard.label4') }}</div>
        <div style="border-bottom: 1px solid #f2f2f2">
          <div data-v-a12ec382="" class="van-cell-group van-hairline--top-bottom">
            <div data-v-a12ec382="" class="van-cell van-field">
              <div class="van-cell__value van-cell__value--alone van-field__value">
                <div class="van-field__body"><input type="text" auto-complete="off"  v-model="cardInfo.bank_no" :placeholder="$t('main.addBankCard.placeholder3')" class="van-field__control" /></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="hgs">
        <div class="nams">{{ $t('main.addBankCard.label5') }}</div>
        <div style="border-bottom: 1px solid #f2f2f2">
          <div data-v-a12ec382="" class="van-cell-group van-hairline--top-bottom">
            <div data-v-a12ec382="" class="van-cell van-field">
              <div class="van-cell__value van-cell__value--alone van-field__value">
                <div class="van-field__body"><input type="text" auto-complete="off"  v-model="cardInfo.bank_address" :placeholder="$t('main.addBankCard.placeholder4')" class="van-field__control" /></div>
              </div>
            </div>
          </div>
        </div>
      </div> -->
      <div class="hgs">
        <div class="nams">{{ $t('main.addBankCard.label6') }}</div>
        <div style="border-bottom: 1px solid #f2f2f2">
          <van-cell-group>
            <van-field v-model="cardInfo.pay_pass" type="password" :placeholder="$t('main.addBankCard.placeholder5')" />
          </van-cell-group>
        </div>
      </div>
      <van-button type="info" style="margin-top: 20px; width: 100%" @click="bindCard">{{ $t('main.addBankCard.label7') }}</van-button>
      <div style="height: 60px"></div>
    </div>
    <div v-if="show" style="position: fixed; width: 100%; height: 100%; top: 0; z-index: 999; background: rgba(0, 0, 0, 0.39)">
      <van-picker :confirm-button-text="$t('main.dealRecord.label6')" :cancel-button-text="$t('main.dealRecord.label7')" style="position: absolute; bottom: 0; left: 0; width: 100%" :title="$t('main.addBankCard.label8')" show-toolbar :columns="banklist" @confirm="onConfirm" @cancel="onCancel" @change="onChange" value-key="bank_name" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'addBankCard',
  data() {
    return {
      cardInfo: {},
      banklist: [],
      show: false,
    };
  },
  created() {
    let that = this;
    that.getBanklist();
  },
  methods: {
    changShow() {
      this.show = !this.show;
    },
    onConfirm(value, index) {
      this.cardInfo.bank = value.bank_name;
      console.log(this.cardInfo.bank);
      this.show = false;
    },
    onChange(picker, value, index) {},
    onCancel() {
      this.show = false;
    },
    getBanklist() {
      let that = this;
      that.$parent.showLoading();

      that.$apiFun
        .post('/api/banklist', {})
        .then(res => {
          if (res.code != 200) {
            that.showTost(0, res.message);
          }
          if (res.code == 200) {
            that.banklist = res.data;
          }
          that.$parent.hideLoading();
        })
        .catch(res => {
          that.$parent.hideLoading();
        });
    },
    bindCard() {
      let userInfo = this.$store.state.userInfo
      let that = this;
      // if (!that.cardInfo.bank_owner) {
      //   that.$parent.showTost(0, that.$t('main.addBankCard.toast1'));
      //   return;
      // }
      if (!that.cardInfo.bank) {
        that.$parent.showTost(0, that.$t('main.addBankCard.toast2'));
        return;
      }
      // if (!that.cardInfo.bank_address) {
      //   that.$parent.showTost(0, that.$t('main.addBankCard.toast3'));
      //   return;
      // }
      if (!that.cardInfo.bank_no) {
        that.$parent.showTost(0, that.$t('main.addBankCard.toast4'));
        return;
      }
      if (!that.cardInfo.pay_pass) {
        that.$parent.showTost(0, that.$t('main.addBankCard.toast5'));
        return;
      }
      if (that.cardInfo.bank_no.length < 8) {
        that.$parent.showTost(0, that.$t('main.addBankCard.toast6'));
        return;
      }
      if (that.cardInfo.pay_pass.length < 6 || that.cardInfo.pay_pass.length > 18) {
        that.$parent.showTost(0, that.$t('main.addBankCard.toast7'));
        return;
      }
      that.$parent.showLoading();
      that.cardInfo.bank_owner = userInfo.realname
      that.$apiFun
        .post('/api/bindcard', that.cardInfo)
        .then(res => {
          if (res.code != 200) {
            that.$parent.showTost(0, res.message);
          }
          if (res.code == 200) {
            that.$parent.showTost(1, that.$t('main.addBankCard.toast8'));
            that.$router.back();
          }
          that.$parent.hideLoading();
        })
        .catch(res => {
          that.$parent.hideLoading();
        });
    },
  },
  mounted() {
    let that = this;
  },
  updated() {
    let that = this;
  },
};
</script>

<style lang="scss" scoped>
.usrse {
  background: #fff;
  box-sizing: border-box;
  padding: 6px 20px 0;
  .nams {
    font-size: 0.38rem;
    color: #000;
    vertical-align: middle;
    margin-top: 10px;
    font-weight: 700;
  }
  .imgsa {
    position: relative;
    height: 2rem;
    border-bottom: 1px solid #f2f2f2;
    padding-bottom: 0.2rem;
    .bisn {
      width: 0.8rem;
      position: absolute;
      bottom: 0.3rem;
      left: 1.4rem;
    }
    img {
      width: 2rem;
      border-radius: 50%;
    }
  }
}
[class*='van-hairline']:after {
  border: none;
}
</style>
