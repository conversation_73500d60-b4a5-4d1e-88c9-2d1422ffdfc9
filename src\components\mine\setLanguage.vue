<template>

  <!-- 主体 -->
  <main>
    <van-nav-bar class="pageTop" :title="$t('main.transRecord.label24')" left-arrow @click-left="$router.back()" />
    <div class="container">
      <div class="card">
        <div class="row" @click="checkLanguage('kor')">
          <div class="label">한국어</div>
          <van-icon :name="locale === 'kor' ? 'passed' : 'circle'" size="18" color="# CF876B"></van-icon>
          
        </div>
        <div class="row" @click="checkLanguage('en')">
          <div class="label">English</div>
          <van-icon :name="locale === 'en' ? 'passed' : 'circle'" size="18" color="# CF876B"></van-icon>
        </div>
      </div>
    </div>
  </main>
</template>

<script>
// const { t, locale } = useI18n()
export default {
  name: 'setLanguage',
  data() {
    return {
      map: [],
      locale: '',
    };
  },
  created() {
    this.locale = localStorage.getItem('locale')
    this.map = new Map([
      ['kor', 'kor'],
      ['en', 'en']
    ])
  },
  methods: {
    checkLanguage(val) {
      this.$i18n.locale = val
      localStorage.setItem('locale', val)
      this.locale = val
    }
  },
};


</script>

<style scoped lang="scss">
.pageTop {
  position: relative !important;
  height: 46px;
}
.container {
  margin-top: 10px;
  padding: 10px 15px;
  .card {
    // padding: 10px;
    background: #fff;
  }
  .row {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .label {
      color: #000;
    }
    img {
      width: 1rem;
    }
  }
}
</style>
