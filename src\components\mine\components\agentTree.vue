<template>
  <div class="treeWrap">
    <AgentTreeItem v-for="(item, index) in data" :key="index" :data="item" @changeChildren="changeChildren"/>
    <!-- <div class="treeLable" v-for="(item, index) in data" :key="index" @click.stop="getChildren(item)">
      <div class="label">
        <van-icon name="arrow" v-if="item.children === undefined"/>
        <van-loading type="spinner" size="16" v-if="item.loading"/>
        <div class="name">{{ item.username }}</div>
      </div>
      <div class="value">{{ item.balance }}</div>
      <div class="children">
        <agent-tree :data="item.children" @changeChildren="changeChildren"/>
      </div>
    </div> -->
  </div>
</template>
<script>
import AgentTreeItem from './agentTreeItem.vue'
export default {
  components: { AgentTreeItem },
  name: 'agent<PERSON>ree',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
    }
  },
  created() {
  },
  methods: {
    async getChildren(item) {
      const pid = item.id
      item.loading = true
      const res = await this.$apiFun.agentChildrenApi({pid})
      if(res.data.length > 0) {
        res.data = res.data.map(item => {
          item.pid = pid
          return item
        })
      }
      const data = {
        data: res.data,
        pid,
      }
      this.$emit('changeChildren', data)
    },
    changeChildren(data){
      this.$emit('changeChildren', data)
    }
  },
};
</script>
<style lang="scss" scoped>
.treeLable {
  display: flex;
  justify-content: space-between;
  border: 1px solid #ccc;
  padding: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  align-items: center;
  flex-wrap: wrap;
  .label {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  .children {
    width: 100%;
  }
}
</style>
